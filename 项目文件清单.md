# IPTV项目文件清单 v2.1

## 📁 核心功能文件

### 🔧 主要功能模块
- **crawler.py** - 爬虫核心模块，负责从各种源获取频道数据
- **m3u_generator.py** - M3U播放列表生成器
- **scheduler.py** - 定时任务调度器，支持自动更新和Git同步
- **test_links.py** - 增强版链接测试工具，支持多线程和详细报告
- **config.py** - 统一配置管理文件

### 🔗 Git同步模块
- **git_sync.py** - Git远程同步核心模块
- **git_sync_example.py** - Git同步功能使用示例和演示
- **git_sync.bat** - Windows Git同步批处理脚本

## 📄 数据文件

### 📺 频道数据
- **cctv_m3u8_links.txt** - 央视频道本地数据源
- **satellite_tv_links.txt** - 省级卫视频道本地数据源
- **iptv.m3u** - 主播放列表文件（39个频道）

### 📊 测试和日志
- **test_results_*.json** - 测试结果报告文件
- **crawler.log** - 系统运行日志
- **iptv_*.m3u** - 自动生成的备份文件

## 🚀 启动脚本

### Windows批处理脚本
- **start_scheduler.bat** - 定时任务启动脚本
- **git_sync.bat** - Git同步工具脚本

## 📖 文档文件

### 项目文档
- **README.md** - 项目主要说明文档
- **项目总结报告.md** - 详细的项目总结报告
- **项目文件清单.md** - 本文件，项目文件清单
- **IPTV M3U 播放列表生成器.md** - 原始项目文档
- **todo.md** - 待办事项清单

### 使用示例
- **example_usage.py** - 功能使用示例脚本
- **quick_test.py** - 快速功能测试脚本

## 🗂️ 系统文件

### Python缓存
- **__pycache__/** - Python字节码缓存目录
  - config.cpython-313.pyc
  - crawler.cpython-313.pyc
  - m3u_generator.cpython-313.pyc

## 📊 文件统计

### 代码文件统计
| 文件名 | 类型 | 行数 | 功能描述 |
|--------|------|------|----------|
| crawler.py | Python | 200+ | 爬虫核心功能 |
| m3u_generator.py | Python | 100+ | M3U文件生成 |
| scheduler.py | Python | 170+ | 定时任务调度 |
| test_links.py | Python | 370+ | 链接测试工具 |
| config.py | Python | 120+ | 配置管理 |
| git_sync.py | Python | 300+ | Git同步功能 |
| git_sync_example.py | Python | 200+ | 使用示例 |
| example_usage.py | Python | 200+ | 功能演示 |
| quick_test.py | Python | 150+ | 快速测试 |

### 总计
- **Python文件**: 9个
- **批处理文件**: 2个
- **数据文件**: 3个
- **文档文件**: 5个
- **总代码行数**: 1800+ 行

## 🎯 功能模块分布

### 核心功能 (60%)
- 爬虫和数据获取
- M3U文件生成
- 配置管理

### 自动化功能 (25%)
- 定时任务调度
- 自动备份管理
- Git远程同步

### 测试和工具 (15%)
- 链接测试和验证
- 功能演示和示例
- 快速测试工具

## 🔄 版本历史

### v2.1 新增文件
- git_sync.py
- git_sync_example.py
- git_sync.bat
- 项目文件清单.md

### v2.0 新增文件
- scheduler.py
- config.py
- start_scheduler.bat
- example_usage.py
- quick_test.py
- README.md

### v1.0 原始文件
- crawler.py
- m3u_generator.py
- test_links.py (简化版)
- cctv_m3u8_links.txt
- satellite_tv_links.txt

## 🛠️ 依赖关系

### 核心依赖
```
requests >= 2.32.3
schedule >= 1.2.2
```

### 模块依赖关系
```
scheduler.py
├── crawler.py
├── m3u_generator.py
├── config.py
└── git_sync.py (可选)

test_links.py
├── config.py
└── requests

git_sync.py
├── config.py
└── subprocess (内置)
```

## 📋 使用流程

### 基本使用
1. 运行 `python crawler.py` 生成播放列表
2. 运行 `python test_links.py` 测试链接
3. 使用生成的 `iptv.m3u` 文件

### 自动化使用
1. 配置 `config.py` 中的参数
2. 运行 `start_scheduler.bat` 启动定时任务
3. 系统自动更新和同步

### Git同步使用
1. 配置 `config.py` 中的Git参数
2. 运行 `git_sync.bat` 或 `python git_sync.py`
3. 播放列表自动同步到远程仓库

## 🎉 项目特色

- **功能完整**: 从数据获取到远程同步的完整解决方案
- **高度自动化**: 定时更新、自动备份、远程同步
- **用户友好**: Windows批处理脚本、详细文档
- **扩展性强**: 模块化设计，易于添加新功能
- **稳定可靠**: 完善的错误处理和日志记录
