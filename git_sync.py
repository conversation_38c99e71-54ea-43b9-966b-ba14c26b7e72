#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV项目Git远程同步脚本
自动将生成的播放列表和相关文件同步到远程Git仓库
"""

import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
import logging

class GitSyncManager:
    """Git同步管理器"""
    
    def __init__(self, repo_url=None, user_name=None, user_email=None):
        """
        初始化Git同步管理器
        
        Args:
            repo_url: Git仓库URL
            user_name: Git用户名
            user_email: Git用户邮箱
        """
        # 默认配置 - 请根据实际情况修改
        self.repo_url = repo_url or "https://gitee.com/syysmq/iptv.git"
        self.user_name = user_name or "syysmq"
        self.user_email = user_email or "<EMAIL>"
        
        # 要同步的文件列表
        self.sync_files = [
            "iptv.m3u",              # 主播放列表
            "README.md",             # 项目说明
            "项目总结报告.md",        # 项目报告
            "cctv_m3u8_links.txt",   # 央视频道数据
            "satellite_tv_links.txt", # 省级卫视数据
        ]
        
        # 可选同步文件（如果存在）
        self.optional_files = [
            "crawler.log",           # 日志文件
        ]
        
        self.logger = self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        logger = logging.getLogger('git_sync')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _run_command(self, cmd, check=True, capture_output=True):
        """
        执行命令

        Args:
            cmd: 命令字符串或列表
            check: 是否检查返回码
            capture_output: 是否捕获输出

        Returns:
            tuple: (returncode, stdout, stderr)
        """
        # 确保命令是列表格式，避免引号和空格问题
        if isinstance(cmd, str):
            # 简单的字符串分割，但保持引号内的内容完整
            import shlex
            cmd = shlex.split(cmd)

        try:
            # 设置正确的编码环境
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            result = subprocess.run(
                cmd,
                capture_output=capture_output,
                text=True,
                check=False,
                encoding='utf-8',
                env=env
            )

            if check and result.returncode != 0:
                self.logger.error(f"命令执行失败: {' '.join(cmd)}")
                self.logger.error(f"错误信息: {result.stderr}")

            return result.returncode, result.stdout, result.stderr

        except Exception as e:
            self.logger.error(f"命令执行异常: {e}")
            return 1, "", str(e)
    
    def _file_exists(self, filepath):
        """检查文件是否存在"""
        try:
            path = Path(filepath)
            return path.exists() and path.is_file()
        except Exception as e:
            self.logger.warning(f"检查文件存在性时出错 {filepath}: {e}")
            return False
    
    def configure_git_user(self):
        """配置Git用户信息"""
        self.logger.info("配置Git用户信息...")

        # 配置用户名和邮箱 - 使用列表格式避免引号问题
        self._run_command(["git", "config", "user.name", self.user_name])
        self._run_command(["git", "config", "user.email", self.user_email])

        # 启用凭证存储
        self._run_command(["git", "config", "credential.helper", "store"])

        self.logger.info(f"Git用户配置完成: {self.user_name} <{self.user_email}>")
    
    def init_repository(self):
        """初始化Git仓库"""
        self.logger.info("初始化Git环境...")

        # 检查是否已经是Git仓库
        returncode, _, _ = self._run_command(["git", "status"], check=False)

        if returncode != 0:
            # 初始化仓库
            self.logger.info("初始化新的Git仓库...")
            self._run_command(["git", "init"])

            # 添加远程仓库
            self.logger.info(f"添加远程仓库: {self.repo_url}")
            returncode, _, stderr = self._run_command(["git", "remote", "add", "origin", self.repo_url], check=False)

            if returncode != 0 and "already exists" in stderr:
                # 如果远程仓库已存在，更新URL
                self._run_command(["git", "remote", "set-url", "origin", self.repo_url])

        # 配置Git用户信息
        self.configure_git_user()
    
    def sync_remote_changes(self):
        """同步远程更改"""
        self.logger.info("同步远程更改...")

        # 获取远程更新
        returncode, _, stderr = self._run_command(["git", "fetch", "--all"], check=False)

        if returncode == 0:
            # 尝试合并远程更改（如果存在）
            self._run_command(["git", "pull", "origin", "master", "--allow-unrelated-histories"], check=False)
        else:
            self.logger.warning(f"获取远程更新失败: {stderr}")
    
    def prepare_files(self):
        """准备要同步的文件"""
        self.logger.info("准备同步文件...")
        
        files_to_sync = []
        
        # 检查必需文件
        for file in self.sync_files:
            if self._file_exists(file):
                files_to_sync.append(file)
                self.logger.info(f"✅ 准备同步: {file}")
            else:
                self.logger.warning(f"⚠️  文件不存在: {file}")
        
        # 检查可选文件
        for file in self.optional_files:
            if self._file_exists(file):
                files_to_sync.append(file)
                self.logger.info(f"✅ 准备同步: {file} (可选)")
        
        # 查找最新的备份文件
        backup_files = list(Path('.').glob('iptv_*.m3u'))
        if backup_files:
            # 按修改时间排序，取最新的
            latest_backup = max(backup_files, key=lambda p: p.stat().st_mtime)
            files_to_sync.append(str(latest_backup))
            self.logger.info(f"✅ 准备同步最新备份: {latest_backup}")
        
        if not files_to_sync:
            raise Exception("没有找到可同步的文件")
        
        return files_to_sync
    
    def commit_changes(self, files_to_sync):
        """提交更改"""
        self.logger.info("提交文件更改...")

        # 添加文件到暂存区 - 使用列表格式避免引号问题
        for file in files_to_sync:
            returncode, _, stderr = self._run_command(["git", "add", file], check=False)
            if returncode != 0:
                self.logger.warning(f"添加文件失败 {file}: {stderr}")
            else:
                self.logger.info(f"成功添加文件: {file}")

        # 创建提交信息
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")  # 使用下划线避免空格问题
        commit_message = f"自动同步IPTV播放列表_{timestamp}"
        
        # 提交更改 - 使用列表格式避免引号和空格问题
        returncode, _, stderr = self._run_command(
            ["git", "commit", "--allow-empty", "-m", commit_message],
            check=False
        )

        if returncode != 0 and "nothing to commit" not in stderr:
            # 如果提交失败，重新配置用户信息后重试
            self.logger.warning("提交失败，重新配置用户信息...")
            self.configure_git_user()

            returncode, _, stderr = self._run_command(
                ["git", "commit", "--allow-empty", "-m", commit_message],
                check=False
            )

            if returncode != 0 and "nothing to commit" not in stderr:
                raise Exception(f"提交失败: {stderr}")

        self.logger.info(f"提交成功: {commit_message}")
    
    def push_to_remote(self):
        """推送到远程仓库"""
        self.logger.info("推送到远程仓库...")
        
        # 推送到远程仓库
        returncode, _, stderr = self._run_command(["git", "push", "-u", "origin", "master"], check=False)
        
        if returncode != 0:
            if "Authentication" in stderr or "Permission denied" in stderr:
                self.logger.error("认证失败！请配置Git凭证：")
                self.logger.error("1. 设置全局用户信息：")
                self.logger.error(f"   git config --global user.name \"{self.user_name}\"")
                self.logger.error(f"   git config --global user.email \"{self.user_email}\"")
                self.logger.error("2. 启用凭证存储：")
                self.logger.error("   git config --global credential.helper store")
                self.logger.error("3. 确保已正确配置远程仓库的访问权限")
                raise Exception("推送失败：认证错误")
            else:
                raise Exception(f"推送失败: {stderr}")
        
        self.logger.info("推送成功！")
    
    def sync(self):
        """执行完整的同步流程"""
        try:
            self.logger.info("=== 开始Git同步流程 ===")
            
            # 1. 初始化仓库
            self.init_repository()
            
            # 2. 同步远程更改
            self.sync_remote_changes()
            
            # 3. 准备文件
            files_to_sync = self.prepare_files()
            
            # 4. 提交更改
            self.commit_changes(files_to_sync)
            
            # 5. 推送到远程
            self.push_to_remote()
            
            self.logger.info("=== Git同步完成 ===")
            self.logger.info(f"文件访问地址: {self.repo_url.replace('.git', '')}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"同步失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="IPTV项目Git同步工具")
    parser.add_argument("--repo", help="Git仓库URL")
    parser.add_argument("--user", help="Git用户名")
    parser.add_argument("--email", help="Git用户邮箱")
    parser.add_argument("--config", action="store_true", help="仅配置Git用户信息")
    
    args = parser.parse_args()
    
    # 创建同步管理器
    sync_manager = GitSyncManager(
        repo_url=args.repo,
        user_name=args.user,
        user_email=args.email
    )
    
    if args.config:
        # 仅配置Git用户信息
        sync_manager.configure_git_user()
        print("Git用户信息配置完成")
    else:
        # 执行完整同步
        success = sync_manager.sync()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
