#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速功能测试脚本
"""

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        import requests
        print("✅ requests 模块导入成功")
    except ImportError as e:
        print(f"❌ requests 模块导入失败: {e}")
        return False
    
    try:
        import schedule
        print("✅ schedule 模块导入成功")
    except ImportError as e:
        print(f"❌ schedule 模块导入失败: {e}")
        return False
    
    try:
        from config import config
        print("✅ config 模块导入成功")
        print(f"   在线源数量: {len(config.ONLINE_SOURCES)}")
        print(f"   省级卫视数量: {len(config.SATELLITE_TV_SOURCES)}")
    except ImportError as e:
        print(f"❌ config 模块导入失败: {e}")
        return False
    
    try:
        from crawler import get_random_user_agent, setup_logging
        print("✅ crawler 模块导入成功")
        print(f"   随机User-Agent: {get_random_user_agent()[:50]}...")
    except ImportError as e:
        print(f"❌ crawler 模块导入失败: {e}")
        return False
    
    return True

def test_file_existence():
    """测试文件存在性"""
    print("\n=== 测试文件存在性 ===")
    
    import os
    
    files_to_check = [
        "crawler.py",
        "m3u_generator.py", 
        "scheduler.py",
        "test_links.py",
        "config.py",
        "cctv_m3u8_links.txt",
        "satellite_tv_links.txt",
        "start_scheduler.bat",
        "README.md"
    ]
    
    all_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
            all_exist = False
    
    return all_exist

def test_m3u_file():
    """测试M3U文件"""
    print("\n=== 测试M3U文件 ===")
    
    import os
    
    if not os.path.exists("iptv.m3u"):
        print("❌ iptv.m3u 文件不存在")
        return False
    
    try:
        with open("iptv.m3u", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"✅ M3U文件读取成功，共 {len(lines)} 行")
        
        # 统计频道数
        channel_count = 0
        for line in lines:
            if line.startswith('#EXTINF:'):
                channel_count += 1
        
        print(f"   频道数量: {channel_count}")
        
        # 检查分组
        cctv_count = sum(1 for line in lines if 'group-title="CCTV"' in line)
        satellite_count = sum(1 for line in lines if 'group-title="Satellite TV"' in line)
        
        print(f"   CCTV频道: {cctv_count}")
        print(f"   省级卫视: {satellite_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ M3U文件读取失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        from config import config
        
        print(f"✅ 定时任务间隔: {config.SCHEDULE_INTERVAL_DAYS} 天")
        print(f"✅ 定时任务时间: {config.SCHEDULE_TIME}")
        print(f"✅ 请求超时: {config.REQUEST_TIMEOUT} 秒")
        print(f"✅ 在线源数量: {len(config.ONLINE_SOURCES)}")
        print(f"✅ 省级卫视数量: {len(config.SATELLITE_TV_SOURCES)}")
        print(f"✅ User-Agent数量: {len(config.USER_AGENTS)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 IPTV项目快速功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("文件存在性", test_file_existence),
        ("M3U文件", test_m3u_file),
        ("配置", test_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {name} 测试通过")
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"💥 {name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
