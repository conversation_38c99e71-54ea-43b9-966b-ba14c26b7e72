import requests
import time
import random
from urllib import parse

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (X11; Linux i686; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/93.0.961.47",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/93.0.961.47",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/91.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (X11; Linux i686; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0"
]

def get_random_user_agent():
    return random.choice(USER_AGENTS)

def ddCalcu(url):
    # This function is specific to Migu API and might not be needed if reading from a pre-parsed m3u file
    # Keeping it here for reference, but it won\"t be used in fetch_migu_m3u8_links if reading from file.
    new_url = parse.urlparse(url)
    para = dict(parse.parse_qsl(new_url.query))
    userid = para.get("userid","")
    timestamp = para.get("timestamp","")
    ProgramID = para.get("ProgramID","")
    Channel_ID = para.get("Channel_ID","")
    puData = para.get("puData","")
    t = userid if userid else "eeeeeeeee" 
    r = timestamp if timestamp else "tttttttttttttt"
    n = ProgramID if ProgramID else "ccccccccc"
    a = Channel_ID if Channel_ID else "nnnnnnnnnnnnnnnn"
    o = puData if puData else ""
    if not o:
        return url
    s = list("2624")
    u = list(t)[int(s[0])] or "e"
    l = list(r)[int(s[1])] or "t"
    c = list(n)[int(s[2])] or "c"
    f = list(a)[len(a)-int(s[3])] or "n"
    d = list(o)
    h = []
    p = 0
    while p*2 < len(d):
        h.append(d[len(d)-p-1])
        if p < len(d) - p -1:
            h.append(o[p])
        if p == 1:
            h.append(u)
        if p == 2:
            h.append(l)
        if p == 3:
            h.append(c)
        if p == 4:
            h.append(f)
        p += 1
    v = "".join(h)
    return url + "&ddCalcu=" + v

def fetch_cctv_m3u8_links():
    links = []
    try:
        with open("cctv_m3u8_links.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(" ") # Split by all spaces
                    channel_name = parts[0]
                    url = parts[-1] # Last part is the URL
                    links.append({"name": channel_name, "url": url})
    except FileNotFoundError:
        print("Error: cctv_m3u8_links.txt not found. Please ensure the file exists.")
    except Exception as e:
        print(f"Error reading cctv_m3u8_links.txt: {e}")
    return links

def fetch_migu_m3u8_links():
    links = []
    try:
        with open("migu_m3u8_links.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("#EXTINF:"):
                    # Parse channel name and URL from the M3U line
                    # Example: #EXTINF:-1 tvg-logo="..." group-title="咪咕「移动」",晴彩广场舞「移动」
                    # http://gslbserv.itv.cmvideo.cn:80/...
                    try:
                        # Extract channel name
                        name_start = line.find(",") + 1
                        channel_name = line[name_start:].strip()
                        
                        # Read the next line for the URL
                        url = next(f).strip()
                        links.append({"name": channel_name, "url": url})
                    except StopIteration:
                        print("Warning: Malformed Migu M3U file, expected URL after #EXTINF line.")
                    except Exception as e:
                        print("Error parsing Migu M3U line: %s. Error: %s" % (line, e))
    except FileNotFoundError:
        print("Error: migu_m3u8_links.txt not found. Please ensure the file exists.")
    except Exception as e:
        print(f"Error reading migu_m3u8_links.txt: {e}")
    return links

if __name__ == "__main__":
    print("Fetching CCTV M3U8 links...")
    cctv_links = fetch_cctv_m3u8_links()
    for link in cctv_links:
        print("Channel: %s, URL: %s" % (link["name"], link["url"]))

    print("\nFetching Migu Video M3U8 links...")
    migu_links = fetch_migu_m3u8_links()
    for link in migu_links:
        print("Channel: %s, URL: %s" % (link["name"], link["url"]))


