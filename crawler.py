import requests
import time
import random
import logging
from urllib import parse
from datetime import datetime

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (X11; Linux i686; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/93.0.961.47",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/93.0.961.47",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/91.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (X11; Linux i686; rv:91.0) Gecko/20100101 Firefox/91.0",
    "Mozilla/5.0 (Linux x86_64; rv:91.0) Gecko/20100101 Firefox/91.0"
]

def get_random_user_agent():
    """获取随机User-Agent"""
    return random.choice(USER_AGENTS)

def setup_logging():
    """设置日志记录"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('crawler.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def validate_m3u8_url(url, timeout=10):
    """
    验证M3U8链接是否有效

    Args:
        url: M3U8链接
        timeout: 超时时间（秒）

    Returns:
        bool: 链接是否有效
    """
    try:
        headers = {'User-Agent': get_random_user_agent()}
        response = requests.head(url, headers=headers, timeout=timeout)
        return response.status_code == 200
    except Exception as e:
        logging.warning(f"验证链接失败 {url}: {e}")
        return False

def random_delay(min_seconds=1, max_seconds=3):
    """随机延时"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

def fetch_satellite_tv_direct_sources():
    """
    直接获取省级卫视源

    Returns:
        list: 省级卫视频道列表
    """
    logger = logging.getLogger(__name__)
    satellite_channels = [
        {"name": "湖南卫视", "url": "http://**************:9901/tsfile/live/0128_1.m3u8"},
        {"name": "浙江卫视", "url": "http://58.57.40.22:9901/tsfile/live/1029_1.m3u8"},
        {"name": "江苏卫视", "url": "http://**************:9901/tsfile/live/0127_1.m3u8"},
        {"name": "东方卫视", "url": "http://**************:9901/tsfile/live/0107_1.m3u8"},
        {"name": "北京卫视", "url": "http://**************:9901/tsfile/live/0122_1.m3u8"},
        {"name": "广东卫视", "url": "http://**************:9901/tsfile/live/0125_1.m3u8"},
        {"name": "深圳卫视", "url": "http://58.57.40.22:9901/tsfile/live/1036_1.m3u8"},
        {"name": "山东卫视", "url": "http://**************:9901/tsfile/live/0131_1.m3u8"},
        {"name": "天津卫视", "url": "http://60.12.183.46:9901/tsfile/live/0120_1.m3u8"},
        {"name": "辽宁卫视", "url": "http://60.12.183.46:9901/tsfile/live/0123_1.m3u8"},
        {"name": "黑龙江卫视", "url": "http://60.12.183.46:9901/tsfile/live/1001_1.m3u8"},
        {"name": "安徽卫视", "url": "http://**************:9901/tsfile/live/0130_1.m3u8"},
        {"name": "河北卫视", "url": "http://**************:9901/tsfile/live/0117_1.m3u8"},
        {"name": "河南卫视", "url": "http://60.12.183.46:9901/tsfile/live/0121_1.m3u8"},
        {"name": "湖北卫视", "url": "http://60.12.183.46:9901/tsfile/live/0116_1.m3u8"},
        {"name": "江西卫视", "url": "http://**************:9901/tsfile/live/0138_1.m3u8"},
        {"name": "四川卫视", "url": "http://58.57.40.22:9901/tsfile/live/1046_1.m3u8"},
        {"name": "重庆卫视", "url": "http://60.12.183.46:9901/tsfile/live/0122_1.m3u8"},
        {"name": "贵州卫视", "url": "http://**************:9901/tsfile/live/0120_1.m3u8"},
        {"name": "云南卫视", "url": "http://**************:9901/tsfile/live/0119_1.m3u8"},
    ]

    links = []
    for channel in satellite_channels:
        links.append({"name": channel["name"], "url": channel["url"]})
        logger.info(f"添加省级卫视: {channel['name']}")

    return links

def ddCalcu(url):
    # This function is specific to Migu API and might not be needed if reading from a pre-parsed m3u file
    # Keeping it here for reference, but it won\"t be used in fetch_migu_m3u8_links if reading from file.
    new_url = parse.urlparse(url)
    para = dict(parse.parse_qsl(new_url.query))
    userid = para.get("userid","")
    timestamp = para.get("timestamp","")
    ProgramID = para.get("ProgramID","")
    Channel_ID = para.get("Channel_ID","")
    puData = para.get("puData","")
    t = userid if userid else "eeeeeeeee" 
    r = timestamp if timestamp else "tttttttttttttt"
    n = ProgramID if ProgramID else "ccccccccc"
    a = Channel_ID if Channel_ID else "nnnnnnnnnnnnnnnn"
    o = puData if puData else ""
    if not o:
        return url
    s = list("2624")
    u = list(t)[int(s[0])] or "e"
    l = list(r)[int(s[1])] or "t"
    c = list(n)[int(s[2])] or "c"
    f = list(a)[len(a)-int(s[3])] or "n"
    d = list(o)
    h = []
    p = 0
    while p*2 < len(d):
        h.append(d[len(d)-p-1])
        if p < len(d) - p -1:
            h.append(o[p])
        if p == 1:
            h.append(u)
        if p == 2:
            h.append(l)
        if p == 3:
            h.append(c)
        if p == 4:
            h.append(f)
        p += 1
    v = "".join(h)
    return url + "&ddCalcu=" + v

def fetch_online_iptv_sources():
    """
    从在线源获取IPTV数据

    Returns:
        dict: 包含央视和省级卫视频道的字典
    """
    logger = logging.getLogger(__name__)
    online_sources = [
        "https://raw.githubusercontent.com/joevess/IPTV/main/m3u/iptv.m3u",
        "https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u",
        "https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u",
        "https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u",
    ]

    all_cctv_links = []
    all_satellite_links = []

    for source_url in online_sources:
        try:
            logger.info(f"正在获取在线源: {source_url}")
            headers = {'User-Agent': get_random_user_agent()}
            response = requests.get(source_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 解析M3U内容
            lines = response.text.split('\n')
            current_channel = None

            for line in lines:
                line = line.strip()
                if line.startswith('#EXTINF:'):
                    # 提取频道名称
                    if ',' in line:
                        current_channel = line.split(',')[-1].strip()
                elif line.startswith('http') and current_channel:
                    # 分类频道
                    if any(keyword in current_channel.upper() for keyword in ['CCTV', '央视']):
                        all_cctv_links.append({"name": current_channel, "url": line})
                    elif '卫视' in current_channel:
                        all_satellite_links.append({"name": current_channel, "url": line})
                    current_channel = None

            random_delay(2, 5)  # 请求间隔

        except Exception as e:
            logger.error(f"获取在线源失败 {source_url}: {e}")
            continue

    return {"cctv": all_cctv_links, "satellite": all_satellite_links}

def fetch_cctv_m3u8_links(use_online=False):
    """
    获取央视M3U8链接

    Args:
        use_online: 是否使用在线源

    Returns:
        list: 央视频道链接列表
    """
    logger = logging.getLogger(__name__)

    if use_online:
        logger.info("尝试从在线源获取央视频道...")
        online_data = fetch_online_iptv_sources()
        if online_data["cctv"]:
            logger.info(f"从在线源获取到 {len(online_data['cctv'])} 个央视频道")
            return online_data["cctv"]

    # 回退到本地文件
    logger.info("从本地文件获取央视频道...")
    links = []
    try:
        with open("cctv_m3u8_links.txt", "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(" ") # Split by all spaces
                    channel_name = parts[0]
                    url = parts[-1] # Last part is the URL
                    links.append({"name": channel_name, "url": url})
        logger.info(f"从本地文件获取到 {len(links)} 个央视频道")
    except FileNotFoundError:
        logger.error("Error: cctv_m3u8_links.txt not found. Please ensure the file exists.")
    except Exception as e:
        logger.error(f"Error reading cctv_m3u8_links.txt: {e}")
    return links

def fetch_satellite_tv_links(use_online=False):
    """
    获取省级卫视M3U8链接

    Args:
        use_online: 是否使用在线源

    Returns:
        list: 省级卫视频道链接列表
    """
    logger = logging.getLogger(__name__)

    if use_online:
        logger.info("尝试从直接源获取省级卫视频道...")
        direct_links = fetch_satellite_tv_direct_sources()
        if direct_links:
            logger.info(f"从直接源获取到 {len(direct_links)} 个省级卫视频道")
            return direct_links

        logger.info("尝试从在线源获取省级卫视频道...")
        online_data = fetch_online_iptv_sources()
        if online_data["satellite"]:
            logger.info(f"从在线源获取到 {len(online_data['satellite'])} 个省级卫视频道")
            return online_data["satellite"]

    # 回退到本地文件
    logger.info("从本地文件获取省级卫视频道...")
    links = []
    try:
        with open("satellite_tv_links.txt", "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line.startswith("#EXTINF:"):
                    # Parse channel name and URL from the M3U line
                    # Example: #EXTINF:-1 tvg-logo="..." group-title="卫视",湖南卫视
                    # http://**************:9901/tsfile/live/0128_1.m3u8
                    try:
                        # Extract channel name
                        name_start = line.find(",") + 1
                        channel_name = line[name_start:].strip()

                        # Read the next line for the URL
                        url = next(f).strip()
                        links.append({"name": channel_name, "url": url})
                    except StopIteration:
                        logger.warning("Warning: Malformed Satellite TV M3U file, expected URL after #EXTINF line.")
                    except Exception as e:
                        logger.error("Error parsing Satellite TV M3U line: %s. Error: %s" % (line, e))
        logger.info(f"从本地文件获取到 {len(links)} 个省级卫视频道")
    except FileNotFoundError:
        logger.error("Error: satellite_tv_links.txt not found. Please ensure the file exists.")
    except Exception as e:
        logger.error(f"Error reading satellite_tv_links.txt: {e}")
    return links

if __name__ == "__main__":
    # 设置日志
    logger = setup_logging()

    print("=== IPTV M3U8 链接获取器 ===")
    print("1. 从本地文件获取")
    print("2. 从在线源获取（推荐）")

    choice = input("请选择获取方式 (1/2，默认2): ").strip()
    use_online = choice != "1"

    logger.info("开始获取IPTV链接...")

    print("\n正在获取央视频道...")
    cctv_links = fetch_cctv_m3u8_links(use_online=use_online)
    print(f"获取到 {len(cctv_links)} 个央视频道")

    print("\n正在获取省级卫视频道...")
    satellite_links = fetch_satellite_tv_links(use_online=use_online)
    print(f"获取到 {len(satellite_links)} 个省级卫视频道")

    # 显示部分结果
    print("\n=== 央视频道示例 ===")
    for i, link in enumerate(cctv_links[:5]):
        print(f"{i+1}. {link['name']}: {link['url']}")
    if len(cctv_links) > 5:
        print(f"... 还有 {len(cctv_links) - 5} 个频道")

    print("\n=== 省级卫视频道示例 ===")
    for i, link in enumerate(satellite_links[:5]):
        print(f"{i+1}. {link['name']}: {link['url']}")
    if len(satellite_links) > 5:
        print(f"... 还有 {len(satellite_links) - 5} 个频道")

    logger.info("链接获取完成")


