@echo off
chcp 65001 >nul
title IPTV Git同步工具

echo.
echo ==========================================
echo           IPTV Git同步工具
echo ==========================================
echo.

:menu
echo 请选择操作：
echo.
echo 1. 执行Git同步
echo 2. 配置Git用户信息
echo 3. 查看配置指南
echo 4. 测试Git连接
echo 5. 退出
echo.

set /p choice=请输入选项 (1-5): 

if "%choice%"=="1" goto sync
if "%choice%"=="2" goto config
if "%choice%"=="3" goto guide
if "%choice%"=="4" goto test
if "%choice%"=="5" goto exit
echo 无效选项，请重新选择
goto menu

:sync
echo.
echo === 执行Git同步 ===
python git_sync.py
echo.
pause
goto menu

:config
echo.
echo === 配置Git用户信息 ===
python git_sync.py --config
echo.
pause
goto menu

:guide
echo.
echo === 配置指南 ===
python git_sync_example.py --guide
echo.
pause
goto menu

:test
echo.
echo === 测试Git连接 ===
echo 检查Git是否已安装...
git --version
if errorlevel 1 (
    echo ❌ Git未安装或未添加到PATH
    echo 请先安装Git: https://git-scm.com/download/win
) else (
    echo ✅ Git已安装
    echo.
    echo 检查Git配置...
    git config user.name
    git config user.email
)
echo.
pause
goto menu

:exit
echo.
echo 👋 再见！
pause
exit
