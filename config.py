#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV项目配置文件
"""

import os
from datetime import time

class Config:
    """配置类"""
    
    # 定时任务配置
    SCHEDULE_INTERVAL_DAYS = 3  # 更新间隔天数
    SCHEDULE_TIME = "09:00"     # 每日更新时间
    MAX_BACKUP_FILES = 7        # 最大备份文件数量
    
    # 网络请求配置
    REQUEST_TIMEOUT = 30        # 请求超时时间（秒）
    MAX_RETRIES = 3            # 最大重试次数
    RETRY_DELAY = 5            # 重试延迟（秒）
    
    # 测试配置
    DEFAULT_TEST_COUNT = 5      # 默认测试频道数
    TEST_TIMEOUT = 10          # 测试超时时间（秒）
    MAX_WORKERS = 5            # 多线程最大工作线程数
    
    # 文件路径配置
    MAIN_M3U_FILE = "iptv.m3u"
    CCTV_LOCAL_FILE = "cctv_m3u8_links.txt"
    SATELLITE_LOCAL_FILE = "satellite_tv_links.txt"
    LOG_FILE = "crawler.log"
    
    # 在线源配置
    ONLINE_SOURCES = [
        "https://raw.githubusercontent.com/joevess/IPTV/main/m3u/iptv.m3u",
        "https://raw.githubusercontent.com/YueChan/Live/main/IPTV.m3u",
        "https://raw.githubusercontent.com/Kimentanm/aptv/master/m3u/iptv.m3u",
        "https://raw.githubusercontent.com/YanG-1989/m3u/main/Gather.m3u",
        "https://raw.githubusercontent.com/fanmingming/live/main/tv/m3u/ipv6.m3u",
        "https://raw.githubusercontent.com/Guovin/iptv-api/gd/output/result.m3u",
        "https://raw.githubusercontent.com/ssili126/tv/main/itvlist.m3u",
        "https://raw.githubusercontent.com/qwerttvv/Beijing-IPTV/master/IPTV-Unicom.m3u",
        "https://raw.githubusercontent.com/BurningC4/Chinese-IPTV/master/TV-IPV4.m3u",
        "https://raw.githubusercontent.com/SPX372928/MyIPTV/main/IPTV.m3u",
        "https://raw.githubusercontent.com/kimwang1978/collect-tv-txt/main/merged_output.m3u",
        "https://raw.githubusercontent.com/Fairy8o/IPTV/main/PDX-V4.m3u",
    ]
    
    # User-Agent列表
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0'
    ]
    
    # 省级卫视直接源配置
    SATELLITE_TV_SOURCES = [
        {"name": "湖南卫视", "url": "http://**************:9901/tsfile/live/0128_1.m3u8"},
        {"name": "浙江卫视", "url": "http://***********:9901/tsfile/live/1029_1.m3u8"},
        {"name": "江苏卫视", "url": "http://**************:9901/tsfile/live/0127_1.m3u8"},
        {"name": "东方卫视", "url": "http://**************:9901/tsfile/live/0107_1.m3u8"},
        {"name": "北京卫视", "url": "http://**************:9901/tsfile/live/0122_1.m3u8"},
        {"name": "广东卫视", "url": "http://**************:9901/tsfile/live/0125_1.m3u8"},
        {"name": "深圳卫视", "url": "http://***********:9901/tsfile/live/1036_1.m3u8"},
        {"name": "山东卫视", "url": "http://**************:9901/tsfile/live/0131_1.m3u8"},
        {"name": "天津卫视", "url": "http://************:9901/tsfile/live/0120_1.m3u8"},
        {"name": "辽宁卫视", "url": "http://************:9901/tsfile/live/0123_1.m3u8"},
        {"name": "黑龙江卫视", "url": "http://************:9901/tsfile/live/1001_1.m3u8"},
        {"name": "安徽卫视", "url": "http://**************:9901/tsfile/live/0130_1.m3u8"},
        {"name": "河北卫视", "url": "http://**************:9901/tsfile/live/0117_1.m3u8"},
        {"name": "河南卫视", "url": "http://************:9901/tsfile/live/0121_1.m3u8"},
        {"name": "湖北卫视", "url": "http://************:9901/tsfile/live/0116_1.m3u8"},
        {"name": "江西卫视", "url": "http://**************:9901/tsfile/live/0138_1.m3u8"},
        {"name": "四川卫视", "url": "http://***********:9901/tsfile/live/1046_1.m3u8"},
        {"name": "重庆卫视", "url": "http://************:9901/tsfile/live/0122_1.m3u8"},
        {"name": "贵州卫视", "url": "http://**************:9901/tsfile/live/0120_1.m3u8"},
        {"name": "云南卫视", "url": "http://**************:9901/tsfile/live/0119_1.m3u8"},
    ]

    # Git同步配置
    GIT_REPO_URL = "https://gitee.com/syysmq/iptv.git"  # Git仓库URL
    GIT_USER_NAME = "syysmq"                                     # Git用户名
    GIT_USER_EMAIL = "<EMAIL>"                          # Git用户邮箱
    GIT_AUTO_SYNC = True                                               # 是否在定时任务中自动同步
    GIT_SYNC_FILES = [                                                  # 要同步的文件列表
        "iptv.m3u",
        "README.md",
        "项目总结报告.md",
        "cctv_m3u8_links.txt",
        "satellite_tv_links.txt"
    ]

    @classmethod
    def get_schedule_time(cls):
        """获取调度时间对象"""
        hour, minute = map(int, cls.SCHEDULE_TIME.split(':'))
        return time(hour, minute)
    
    @classmethod
    def get_backup_filename(cls, timestamp_str):
        """生成备份文件名"""
        return f"iptv_{timestamp_str}.m3u"
    
    @classmethod
    def get_test_result_filename(cls, timestamp_str):
        """生成测试结果文件名"""
        return f"test_results_{timestamp_str}.json"
    
    @classmethod
    def load_from_file(cls, config_file="config.ini"):
        """从配置文件加载设置（可选功能）"""
        # 这里可以实现从INI文件读取配置的功能
        # 暂时使用默认配置
        pass
    
    @classmethod
    def save_to_file(cls, config_file="config.ini"):
        """保存配置到文件（可选功能）"""
        # 这里可以实现保存配置到INI文件的功能
        pass

# 创建全局配置实例
config = Config()
