#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV链接测试脚本
测试生成的M3U文件中的链接是否有效
"""

import requests
import time
import random
import json
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from crawler import get_random_user_agent, setup_logging

def test_single_channel(channel, timeout=10):
    """
    测试单个频道链接

    Args:
        channel: 频道信息字典 {"name": "频道名", "url": "链接"}
        timeout: 超时时间

    Returns:
        dict: 测试结果
    """
    result = {
        "name": channel["name"],
        "url": channel["url"],
        "status": "unknown",
        "status_code": None,
        "response_time": None,
        "error": None
    }

    try:
        start_time = time.time()
        headers = {'User-Agent': get_random_user_agent()}
        response = requests.head(channel['url'], headers=headers, timeout=timeout, allow_redirects=True)
        response_time = time.time() - start_time

        result["status_code"] = response.status_code
        result["response_time"] = round(response_time, 2)

        # 判断链接状态
        if response.status_code in [200, 206, 302]:
            result["status"] = "valid"
        elif response.status_code in [301, 307, 308]:
            result["status"] = "redirect"
        else:
            result["status"] = "invalid"

    except requests.exceptions.Timeout:
        result["status"] = "timeout"
        result["error"] = "请求超时"
    except Exception as e:
        result["status"] = "error"
        result["error"] = str(e)

    return result

def test_m3u_file(filename="iptv.m3u", max_test=5, use_threading=False, max_workers=5):
    """
    测试M3U文件中的链接

    Args:
        filename: M3U文件名
        max_test: 最大测试链接数，0表示测试所有
        use_threading: 是否使用多线程
        max_workers: 最大线程数
    """
    logger = setup_logging()

    print(f"=== 测试 {filename} 中的链接 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return

    # 解析M3U文件
    channels = []
    current_channel = None
    current_group = None

    for line in lines:
        line = line.strip()
        if line.startswith('#EXTINF:'):
            # 提取频道名称和分组
            if ',' in line:
                current_channel = line.split(',')[-1].strip()
                # 提取分组信息
                if 'group-title=' in line:
                    group_start = line.find('group-title="') + 13
                    group_end = line.find('"', group_start)
                    if group_end > group_start:
                        current_group = line[group_start:group_end]
        elif line.startswith('http') and current_channel:
            channels.append({
                "name": current_channel,
                "url": line,
                "group": current_group or "未分组"
            })
            current_channel = None
            current_group = None

    print(f"找到 {len(channels)} 个频道")

    # 选择要测试的频道
    if max_test > 0 and max_test < len(channels):
        test_channels = random.sample(channels, max_test)
        print(f"随机选择 {len(test_channels)} 个频道进行测试")
    else:
        test_channels = channels
        print(f"测试所有 {len(test_channels)} 个频道")

    # 按分组统计
    groups = {}
    for channel in test_channels:
        group = channel.get("group", "未分组")
        if group not in groups:
            groups[group] = []
        groups[group].append(channel)

    print(f"分组统计: {', '.join([f'{k}({len(v)})' for k, v in groups.items()])}")

    # 开始测试
    results = []
    start_time = time.time()

    if use_threading and len(test_channels) > 1:
        print(f"\n🚀 使用多线程测试 (最大{max_workers}个线程)...")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_channel = {executor.submit(test_single_channel, channel): channel for channel in test_channels}

            for i, future in enumerate(as_completed(future_to_channel), 1):
                result = future.result()
                results.append(result)

                # 显示进度
                status_icon = {
                    "valid": "✅",
                    "redirect": "🔄",
                    "invalid": "❌",
                    "timeout": "⏰",
                    "error": "💥"
                }.get(result["status"], "❓")

                print(f"[{i:2d}/{len(test_channels)}] {status_icon} {result['name'][:20]:20} | {result['status']:8} | {result.get('response_time', 0):5.2f}s")
    else:
        print(f"\n🔍 逐个测试频道...")
        for i, channel in enumerate(test_channels, 1):
            print(f"\n[{i:2d}/{len(test_channels)}] 测试: {channel['name']}")
            print(f"   分组: {channel.get('group', '未分组')}")
            print(f"   URL: {channel['url']}")

            result = test_single_channel(channel)
            results.append(result)

            # 显示结果
            status_icon = {
                "valid": "✅",
                "redirect": "🔄",
                "invalid": "❌",
                "timeout": "⏰",
                "error": "💥"
            }.get(result["status"], "❓")

            if result["status"] == "valid":
                print(f"   {status_icon} 状态: {result['status_code']} - 链接有效 ({result['response_time']}s)")
            elif result["status"] == "redirect":
                print(f"   {status_icon} 状态: {result['status_code']} - 重定向 ({result['response_time']}s)")
            elif result["status"] == "invalid":
                print(f"   {status_icon} 状态: {result['status_code']} - 链接无效 ({result['response_time']}s)")
            elif result["status"] == "timeout":
                print(f"   {status_icon} 超时 - 链接可能无效")
            else:
                print(f"   {status_icon} 错误: {result['error']}")

            # 记录日志
            if result["status"] == "valid":
                logger.info(f"频道 {channel['name']} 测试通过")
            else:
                logger.warning(f"频道 {channel['name']} 测试失败: {result['status']}")

            # 随机延时避免请求过快
            if not use_threading and i < len(test_channels):
                delay = random.uniform(0.5, 2)
                time.sleep(delay)

    # 统计结果
    total_time = time.time() - start_time
    valid_count = sum(1 for r in results if r["status"] == "valid")
    redirect_count = sum(1 for r in results if r["status"] == "redirect")
    invalid_count = sum(1 for r in results if r["status"] == "invalid")
    timeout_count = sum(1 for r in results if r["status"] == "timeout")
    error_count = sum(1 for r in results if r["status"] == "error")

    success_rate = (valid_count + redirect_count * 0.5) / len(results) * 100 if results else 0

    print(f"\n📊 测试结果统计:")
    print(f"   总测试数: {len(results)}")
    print(f"   ✅ 有效: {valid_count}")
    print(f"   🔄 重定向: {redirect_count}")
    print(f"   ❌ 无效: {invalid_count}")
    print(f"   ⏰ 超时: {timeout_count}")
    print(f"   💥 错误: {error_count}")
    print(f"   📈 成功率: {success_rate:.1f}%")
    print(f"   ⏱️  总耗时: {total_time:.1f}秒")

    # 按分组统计
    print(f"\n📋 分组统计:")
    for group_name, group_channels in groups.items():
        group_results = [r for r in results if any(c["name"] == r["name"] for c in group_channels)]
        group_valid = sum(1 for r in group_results if r["status"] == "valid")
        group_total = len(group_results)
        group_rate = group_valid / group_total * 100 if group_total > 0 else 0
        print(f"   {group_name}: {group_valid}/{group_total} ({group_rate:.1f}%)")

    # 保存详细结果到文件
    save_results_to_file(results, filename)

    return results

def save_results_to_file(results, original_filename):
    """保存测试结果到文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    result_filename = f"test_results_{timestamp}.json"

    try:
        # 准备保存的数据
        save_data = {
            "test_time": datetime.now().isoformat(),
            "original_file": original_filename,
            "total_channels": len(results),
            "summary": {
                "valid": sum(1 for r in results if r["status"] == "valid"),
                "redirect": sum(1 for r in results if r["status"] == "redirect"),
                "invalid": sum(1 for r in results if r["status"] == "invalid"),
                "timeout": sum(1 for r in results if r["status"] == "timeout"),
                "error": sum(1 for r in results if r["status"] == "error")
            },
            "results": results
        }

        with open(result_filename, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"   💾 详细结果已保存到: {result_filename}")

    except Exception as e:
        print(f"   ⚠️  保存结果文件失败: {e}")

def test_specific_channels(channel_names, filename="iptv.m3u"):
    """测试指定名称的频道"""
    logger = setup_logging()

    print(f"=== 测试指定频道 ===")

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return

    # 解析M3U文件
    all_channels = []
    current_channel = None

    for line in lines:
        line = line.strip()
        if line.startswith('#EXTINF:'):
            if ',' in line:
                current_channel = line.split(',')[-1].strip()
        elif line.startswith('http') and current_channel:
            all_channels.append({"name": current_channel, "url": line})
            current_channel = None

    # 查找指定频道
    test_channels = []
    for name in channel_names:
        found = False
        for channel in all_channels:
            if name.lower() in channel["name"].lower():
                test_channels.append(channel)
                found = True
                break
        if not found:
            print(f"⚠️  未找到频道: {name}")

    if not test_channels:
        print("❌ 没有找到任何指定的频道")
        return

    print(f"找到 {len(test_channels)} 个匹配的频道")

    # 测试频道
    for i, channel in enumerate(test_channels, 1):
        print(f"\n[{i}/{len(test_channels)}] 测试: {channel['name']}")
        result = test_single_channel(channel)

        if result["status"] == "valid":
            print(f"   ✅ 链接有效 ({result['response_time']}s)")
        else:
            print(f"   ❌ 链接无效: {result.get('error', result['status'])}")

def interactive_test():
    """交互式测试模式"""
    print("=== IPTV链接测试工具 ===")
    print("1. 快速测试 (随机5个频道)")
    print("2. 完整测试 (所有频道)")
    print("3. 多线程测试 (所有频道)")
    print("4. 指定频道测试")
    print("5. 自定义测试")

    choice = input("\n请选择测试模式 (1-5): ").strip()

    if choice == "1":
        test_m3u_file(max_test=5)
    elif choice == "2":
        test_m3u_file(max_test=0)
    elif choice == "3":
        test_m3u_file(max_test=0, use_threading=True)
    elif choice == "4":
        channels_input = input("请输入频道名称 (用逗号分隔): ").strip()
        if channels_input:
            channel_names = [name.strip() for name in channels_input.split(',')]
            test_specific_channels(channel_names)
        else:
            print("❌ 未输入频道名称")
    elif choice == "5":
        filename = input("M3U文件名 (默认: iptv.m3u): ").strip() or "iptv.m3u"
        max_test = input("最大测试数量 (0=全部, 默认: 10): ").strip()
        max_test = int(max_test) if max_test.isdigit() else 10

        use_threading = input("是否使用多线程? (y/N): ").strip().lower() == 'y'
        max_workers = 5
        if use_threading:
            workers_input = input("线程数 (默认: 5): ").strip()
            max_workers = int(workers_input) if workers_input.isdigit() else 5

        test_m3u_file(filename, max_test, use_threading, max_workers)
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == "--all":
            test_m3u_file(max_test=0)
        elif sys.argv[1] == "--thread":
            test_m3u_file(max_test=0, use_threading=True)
        elif sys.argv[1] == "--quick":
            test_m3u_file(max_test=5)
        else:
            print("用法:")
            print("  python test_links.py           # 交互式模式")
            print("  python test_links.py --quick   # 快速测试5个频道")
            print("  python test_links.py --all     # 测试所有频道")
            print("  python test_links.py --thread  # 多线程测试所有频道")
    else:
        # 交互式模式
        interactive_test()
