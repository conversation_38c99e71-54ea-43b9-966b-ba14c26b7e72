#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV链接测试脚本
测试生成的M3U文件中的链接是否有效
"""

import requests
import time
import random
from crawler import get_random_user_agent, setup_logging

def test_m3u_file(filename="iptv.m3u", max_test=5):
    """
    测试M3U文件中的链接
    
    Args:
        filename: M3U文件名
        max_test: 最大测试链接数
    """
    logger = setup_logging()
    
    print(f"=== 测试 {filename} 中的链接 ===")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return
    
    # 解析M3U文件
    channels = []
    current_channel = None
    
    for line in lines:
        line = line.strip()
        if line.startswith('#EXTINF:'):
            # 提取频道名称
            if ',' in line:
                current_channel = line.split(',')[-1].strip()
        elif line.startswith('http') and current_channel:
            channels.append({"name": current_channel, "url": line})
            current_channel = None
    
    print(f"找到 {len(channels)} 个频道")
    
    # 随机选择要测试的频道
    test_channels = random.sample(channels, min(max_test, len(channels)))
    
    print(f"随机测试 {len(test_channels)} 个频道:")
    
    success_count = 0
    for i, channel in enumerate(test_channels, 1):
        print(f"\n{i}. 测试频道: {channel['name']}")
        print(f"   URL: {channel['url']}")
        
        try:
            headers = {'User-Agent': get_random_user_agent()}
            response = requests.head(channel['url'], headers=headers, timeout=10, allow_redirects=True)

            # 对于流媒体，200, 302, 206等状态码都可能是正常的
            if response.status_code in [200, 206, 302]:
                print(f"   ✅ 状态: {response.status_code} - 链接有效")
                success_count += 1
                logger.info(f"频道 {channel['name']} 测试通过")
            elif response.status_code in [301, 307, 308]:
                print(f"   🔄 状态: {response.status_code} - 重定向，可能有效")
                success_count += 0.5  # 半分
                logger.info(f"频道 {channel['name']} 重定向")
            else:
                print(f"   ❌ 状态: {response.status_code} - 链接无效")
                logger.warning(f"频道 {channel['name']} 返回状态码 {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"   ❌ 超时 - 链接可能无效")
            logger.error(f"频道 {channel['name']} 测试超时")
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            logger.error(f"频道 {channel['name']} 测试失败: {e}")
        
        # 随机延时避免请求过快
        if i < len(test_channels):
            delay = random.uniform(1, 3)
            time.sleep(delay)
    
    print(f"\n📊 测试结果:")
    print(f"   测试频道数: {len(test_channels)}")
    print(f"   成功数: {success_count}")
    print(f"   成功率: {success_count/len(test_channels)*100:.1f}%")

if __name__ == "__main__":
    test_m3u_file()
