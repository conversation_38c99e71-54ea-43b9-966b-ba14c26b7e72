def generate_m3u_playlist(cctv_links, migu_links, output_file="iptv.m3u"):
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("#EXTM3U\n")

        # Add CCTV links
        f.write("#EXTGRP:CCTV\n")
        for link in cctv_links:
            f.write(f'#EXTINF:-1 group-title="CCTV",{link["name"]}\n')
            f.write(f'{link["url"]}\n')

        # Add Migu Video links
        f.write("#EXTGRP:Migu Video\n")
        for link in migu_links:
            f.write(f'#EXTINF:-1 group-title="Migu Video",{link["name"]}\n')
            f.write(f'{link["url"]}\n')

if __name__ == "__main__":
    from crawler import fetch_cctv_m3u8_links, fetch_migu_m3u8_links

    print("Generating M3U playlist...")
    cctv_links = fetch_cctv_m3u8_links()
    migu_links = fetch_migu_m3u8_links()
    generate_m3u_playlist(cctv_links, migu_links)
    print("M3U playlist generated successfully as iptv.m3u")


