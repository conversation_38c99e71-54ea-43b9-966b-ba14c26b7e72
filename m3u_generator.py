def generate_m3u_playlist(cctv_links, satellite_links, output_file="iptv.m3u"):
    """
    生成M3U播放列表文件

    Args:
        cctv_links: 央视频道链接列表
        satellite_links: 省级卫视链接列表
        output_file: 输出文件名
    """
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("#EXTM3U\n")

        # 添加央视频道
        f.write("#EXTGRP:CCTV\n")
        for link in cctv_links:
            f.write(f'#EXTINF:-1 group-title="CCTV",{link["name"]}\n')
            f.write(f'{link["url"]}\n')

        # 添加省级卫视频道
        f.write("#EXTGRP:Satellite TV\n")
        for link in satellite_links:
            f.write(f'#EXTINF:-1 group-title="Satellite TV",{link["name"]}\n')
            f.write(f'{link["url"]}\n')

if __name__ == "__main__":
    from crawler import fetch_cctv_m3u8_links, fetch_satellite_tv_links, setup_logging, validate_m3u8_url

    # 设置日志
    logger = setup_logging()

    print("=== IPTV M3U 播放列表生成器 ===")
    print("1. 使用本地数据源")
    print("2. 使用在线数据源（推荐，数据更新）")

    choice = input("请选择数据源 (1/2，默认2): ").strip()
    use_online = choice != "1"

    # 询问是否验证链接
    validate_choice = input("是否验证链接有效性？(y/N，默认N): ").strip().lower()
    validate_links = validate_choice == 'y'

    print("\n正在生成M3U播放列表...")
    logger.info("开始生成M3U播放列表")

    cctv_links = fetch_cctv_m3u8_links(use_online=use_online)
    satellite_links = fetch_satellite_tv_links(use_online=use_online)

    print(f"找到 {len(cctv_links)} 个央视频道")
    print(f"找到 {len(satellite_links)} 个省级卫视频道")

    # 验证链接（如果用户选择）
    if validate_links and (cctv_links or satellite_links):
        print("\n正在验证链接有效性...")
        valid_cctv = []
        valid_satellite = []

        for i, link in enumerate(cctv_links):
            print(f"验证央视频道 {i+1}/{len(cctv_links)}: {link['name']}")
            if validate_m3u8_url(link['url']):
                valid_cctv.append(link)
                logger.info(f"央视频道 {link['name']} 验证通过")
            else:
                logger.warning(f"央视频道 {link['name']} 验证失败")

        for i, link in enumerate(satellite_links):
            print(f"验证省级卫视频道 {i+1}/{len(satellite_links)}: {link['name']}")
            if validate_m3u8_url(link['url']):
                valid_satellite.append(link)
                logger.info(f"省级卫视频道 {link['name']} 验证通过")
            else:
                logger.warning(f"省级卫视频道 {link['name']} 验证失败")

        cctv_links = valid_cctv
        satellite_links = valid_satellite
        print(f"验证后剩余: {len(cctv_links)} 个央视频道, {len(satellite_links)} 个省级卫视频道")

    if cctv_links or satellite_links:
        generate_m3u_playlist(cctv_links, satellite_links)
        print("✅ M3U播放列表已成功生成为 iptv.m3u")
        logger.info("M3U播放列表生成完成")

        # 显示统计信息
        total_channels = len(cctv_links) + len(satellite_links)
        print(f"\n📊 统计信息:")
        print(f"   总频道数: {total_channels}")
        print(f"   央视频道: {len(cctv_links)}")
        print(f"   省级卫视频道: {len(satellite_links)}")
        print(f"   文件位置: iptv.m3u")
    else:
        print("❌ 没有找到任何频道数据，请检查数据源")
        logger.error("没有找到任何频道数据")


