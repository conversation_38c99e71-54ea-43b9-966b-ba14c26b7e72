#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git同步功能使用示例
演示如何配置和使用Git同步功能
"""

from git_sync import GitSyncManager
from config import Config

def example_basic_sync():
    """基本同步示例"""
    print("=== 基本Git同步示例 ===")
    
    # 使用默认配置创建同步管理器
    sync_manager = GitSyncManager()
    
    # 执行同步
    success = sync_manager.sync()
    
    if success:
        print("✅ 同步成功！")
    else:
        print("❌ 同步失败！")

def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置同步示例 ===")
    
    # 使用自定义配置
    sync_manager = GitSyncManager(
        repo_url="https://gitee.com/your-username/my-iptv.git",
        user_name="your-username",
        user_email="<EMAIL>"
    )
    
    # 执行同步
    success = sync_manager.sync()
    
    if success:
        print("✅ 自定义配置同步成功！")
    else:
        print("❌ 自定义配置同步失败！")

def example_config_only():
    """仅配置Git用户信息示例"""
    print("\n=== 配置Git用户信息示例 ===")
    
    sync_manager = GitSyncManager(
        user_name="your-username",
        user_email="<EMAIL>"
    )
    
    # 仅配置Git用户信息
    sync_manager.configure_git_user()
    print("✅ Git用户信息配置完成")

def example_with_config_file():
    """使用配置文件的示例"""
    print("\n=== 使用配置文件同步示例 ===")
    
    # 使用config.py中的配置
    sync_manager = GitSyncManager(
        repo_url=Config.GIT_REPO_URL,
        user_name=Config.GIT_USER_NAME,
        user_email=Config.GIT_USER_EMAIL
    )
    
    # 执行同步
    success = sync_manager.sync()
    
    if success:
        print("✅ 配置文件同步成功！")
    else:
        print("❌ 配置文件同步失败！")

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "="*60)
    print("📋 Git同步配置指南")
    print("="*60)
    
    print("\n1. 修改config.py中的Git配置：")
    print("   GIT_REPO_URL = 'https://gitee.com/your-username/iptv-playlist.git'")
    print("   GIT_USER_NAME = 'your-username'")
    print("   GIT_USER_EMAIL = '<EMAIL>'")
    print("   GIT_AUTO_SYNC = True  # 启用自动同步")
    
    print("\n2. 创建Gitee仓库：")
    print("   - 登录Gitee (https://gitee.com)")
    print("   - 创建新仓库 'iptv-playlist'")
    print("   - 复制仓库URL到配置文件")
    
    print("\n3. 配置Git凭证：")
    print("   git config --global user.name 'your-username'")
    print("   git config --global user.email '<EMAIL>'")
    print("   git config --global credential.helper store")
    
    print("\n4. 使用方式：")
    print("   # 手动同步")
    print("   python git_sync.py")
    print("   ")
    print("   # 自定义配置同步")
    print("   python git_sync.py --repo https://gitee.com/user/repo.git --user username --email <EMAIL>")
    print("   ")
    print("   # 仅配置Git用户信息")
    print("   python git_sync.py --config")
    
    print("\n5. 集成到定时任务：")
    print("   - 在config.py中设置 GIT_AUTO_SYNC = True")
    print("   - 运行定时任务时会自动同步到Git仓库")
    
    print("\n6. 同步的文件：")
    for file in Config.GIT_SYNC_FILES:
        print(f"   - {file}")
    print("   - 最新的备份文件 (iptv_*.m3u)")

def interactive_demo():
    """交互式演示"""
    print("\n" + "="*60)
    print("🚀 Git同步功能交互式演示")
    print("="*60)
    
    while True:
        print("\n请选择操作：")
        print("1. 基本同步演示")
        print("2. 自定义配置演示")
        print("3. 仅配置Git用户信息")
        print("4. 使用配置文件同步")
        print("5. 显示配置指南")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选项 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                example_basic_sync()
            elif choice == '2':
                example_custom_config()
            elif choice == '3':
                example_config_only()
            elif choice == '4':
                example_with_config_file()
            elif choice == '5':
                show_configuration_guide()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def main():
    """主函数"""
    print("🔧 IPTV项目Git同步功能演示")
    
    # 检查是否有命令行参数
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == '--guide':
            show_configuration_guide()
        elif sys.argv[1] == '--basic':
            example_basic_sync()
        elif sys.argv[1] == '--config':
            example_config_only()
        else:
            print("可用参数：")
            print("  --guide  显示配置指南")
            print("  --basic  基本同步演示")
            print("  --config 配置Git用户信息")
    else:
        # 交互式演示
        interactive_demo()

if __name__ == "__main__":
    main()
