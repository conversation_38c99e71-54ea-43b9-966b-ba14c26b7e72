# IPTV M3U 播放列表生成器

## 简介

这是一个自动化程序，用于爬取央视网和咪咕视频的m3u8播放地址，生成标准的m3u播放列表文件，并可自动托管到Gitee仓库。

## 功能

- 爬取央视网和咪咕视频的最新m3u8播放地址。
- 生成符合M3U标准的播放列表文件（iptv.m3u）。
- 集成反爬策略，包括随机延时和User-Agent轮换。
- 错误处理和日志记录。

## 文件说明

- `crawler.py`: 爬虫核心代码，负责从央视网和咪咕视频获取m3u8链接。
- `m3u_generator.py`: M3U播放列表生成器，将获取到的链接整理成m3u格式。
- `iptv.m3u`: 生成的M3U播放列表文件。
- `cctv_m3u8_links.txt`: 央视网m3u8链接的本地缓存文件。
- `migu_m3u8_links.txt`: 咪咕视频m3u8链接的本地缓存文件。

## 使用方法

1. **克隆仓库**：

   ```bash
   git clone https://gitee.com/syysmq/iptv.git
   cd iptv
   ```

2. **运行程序**：

   ```bash
   python3 m3u_generator.py
   ```

   程序将自动执行爬取和M3U文件生成。

3. **更新Gitee仓库**：

   由于Gitee仓库需要身份验证，程序无法自动推送。请在程序运行后，手动将更新后的 `iptv.m3u` 文件推送到您的Gitee仓库。

   ```bash
   git add iptv.m3u
   git commit -m "Update iptv.m3u"
   git push
   ```

## 注意事项

- **合规性**：本程序仅用于个人学习和研究，请勿用于传播收费内容或任何非法用途。
- **反爬策略**：程序已内置随机延时和User-Agent轮换，但仍可能因网站反爬策略调整而失效。如遇问题，请检查 `crawler.py` 中的数据提取逻辑。
- **代理IP池**：目前未集成代理IP池功能，如需更强的反爬能力，可自行扩展。
- **定期更新**：央视网和咪咕视频的接口可能变更，建议定期检查并更新 `crawler.py` 中的数据提取逻辑。


