#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV定时任务调度器
每3天自动更新IPTV播放列表
"""

import schedule
import time
import logging
import os
import sys
from datetime import datetime, timedelta
from m3u_generator import generate_m3u_playlist
from crawler import fetch_cctv_m3u8_links, fetch_satellite_tv_links, setup_logging, validate_m3u8_url
from config import Config

class IPTVScheduler:
    """IPTV定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.logger = setup_logging()
        self.last_update = None
        self.update_interval_days = 3
        
    def update_iptv_playlist(self):
        """更新IPTV播放列表"""
        try:
            self.logger.info("开始定时更新IPTV播放列表...")
            print(f"\n=== {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 开始自动更新 ===")
            
            # 获取频道数据（优先使用在线源）
            print("正在获取央视频道...")
            cctv_links = fetch_cctv_m3u8_links(use_online=True)
            
            print("正在获取省级卫视频道...")
            satellite_links = fetch_satellite_tv_links(use_online=True)
            
            print(f"获取到 {len(cctv_links)} 个央视频道")
            print(f"获取到 {len(satellite_links)} 个省级卫视频道")
            
            if cctv_links or satellite_links:
                # 生成M3U文件
                output_file = f"iptv_{datetime.now().strftime('%Y%m%d_%H%M%S')}.m3u"
                generate_m3u_playlist(cctv_links, satellite_links, output_file)
                
                # 同时更新主文件
                generate_m3u_playlist(cctv_links, satellite_links, "iptv.m3u")
                
                self.last_update = datetime.now()
                
                print(f"✅ 播放列表更新成功！")
                print(f"   主文件: iptv.m3u")
                print(f"   备份文件: {output_file}")
                print(f"   总频道数: {len(cctv_links) + len(satellite_links)}")
                print(f"   下次更新时间: {(self.last_update + timedelta(days=self.update_interval_days)).strftime('%Y-%m-%d %H:%M:%S')}")
                
                self.logger.info(f"定时更新完成，生成文件: {output_file}")
                
                # 清理旧的备份文件（保留最近7个）
                self.cleanup_old_backups()

                # Git同步（如果启用）
                if Config.GIT_AUTO_SYNC:
                    self.sync_to_git()

            else:
                print("❌ 未获取到任何频道数据")
                self.logger.error("定时更新失败：未获取到频道数据")
                
        except Exception as e:
            print(f"❌ 定时更新失败: {e}")
            self.logger.error(f"定时更新失败: {e}")
    
    def cleanup_old_backups(self):
        """清理旧的备份文件，保留最近7个"""
        try:
            # 获取所有备份文件
            backup_files = []
            for file in os.listdir('.'):
                if file.startswith('iptv_') and file.endswith('.m3u') and file != 'iptv.m3u':
                    backup_files.append(file)
            
            # 按时间排序
            backup_files.sort(reverse=True)
            
            # 删除超过7个的旧文件
            if len(backup_files) > 7:
                for old_file in backup_files[7:]:
                    try:
                        os.remove(old_file)
                        self.logger.info(f"删除旧备份文件: {old_file}")
                    except Exception as e:
                        self.logger.warning(f"删除文件失败 {old_file}: {e}")
                        
        except Exception as e:
            self.logger.warning(f"清理备份文件失败: {e}")

    def sync_to_git(self):
        """同步到Git仓库"""
        try:
            print("正在同步到Git仓库...")
            self.logger.info("开始Git同步...")

            # 动态导入git_sync模块
            try:
                from git_sync import GitSyncManager
            except ImportError:
                self.logger.warning("git_sync模块未找到，跳过Git同步")
                print("⚠️  Git同步模块未找到，跳过同步")
                return

            # 创建Git同步管理器
            sync_manager = GitSyncManager(
                repo_url=Config.GIT_REPO_URL,
                user_name=Config.GIT_USER_NAME,
                user_email=Config.GIT_USER_EMAIL
            )

            # 执行同步
            success = sync_manager.sync()

            if success:
                print("✅ Git同步成功！")
                self.logger.info("Git同步完成")
            else:
                print("❌ Git同步失败")
                self.logger.error("Git同步失败")

        except Exception as e:
            print(f"❌ Git同步异常: {e}")
            self.logger.error(f"Git同步异常: {e}")

    def start_scheduler(self):
        """启动定时任务调度器"""
        print("=== IPTV定时任务调度器 ===")
        print(f"更新间隔: 每{self.update_interval_days}天")
        print(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 设置定时任务 - 每3天的上午9点执行
        schedule.every(self.update_interval_days).days.at("09:00").do(self.update_iptv_playlist)
        
        # 也可以立即执行一次
        choice = input("\n是否立即执行一次更新？(y/N): ").strip().lower()
        if choice == 'y':
            self.update_iptv_playlist()
        
        print(f"\n⏰ 定时任务已启动，每{self.update_interval_days}天上午9:00自动更新")
        print("按 Ctrl+C 停止调度器")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            print("\n\n⏹️  定时任务调度器已停止")
            self.logger.info("定时任务调度器停止")
    
    def run_once(self):
        """运行一次更新任务"""
        print("=== 手动执行更新任务 ===")
        self.update_iptv_playlist()

def main():
    """主函数"""
    scheduler = IPTVScheduler()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        # 只运行一次
        scheduler.run_once()
    else:
        # 启动定时调度器
        scheduler.start_scheduler()

if __name__ == "__main__":
    main()
