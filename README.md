# IPTV播放列表生成器

一个功能完整的IPTV M3U播放列表生成器，支持自动爬取央视频道和省级卫视频道，具备定时更新、多线程测试等高级功能。

## 🚀 新功能特性

### 1. Git远程同步
- **自动同步**: 支持将生成的播放列表自动同步到Git仓库（GitHub/Gitee）
- **配置灵活**: 支持自定义仓库URL、用户信息等配置
- **集成定时任务**: 可在定时更新后自动同步到远程仓库
- **多文件同步**: 同步播放列表、文档、数据文件等多个文件
- **Windows友好**: 提供批处理脚本一键操作

### 2. 定时任务调度器
- **自动更新**: 每3天自动更新播放列表
- **灵活配置**: 可自定义更新时间和间隔
- **备份管理**: 自动保留最近7个备份文件
- **一键启动**: 支持Windows批处理文件启动

### 2. 增强的在线源
- **12个在线源**: 大幅增加了在线IPTV源数量
- **智能聚合**: 自动从多个GitHub仓库获取最新频道
- **容错机制**: 单个源失败不影响整体运行
- **配置化管理**: 所有源地址统一在配置文件中管理

### 3. 完善的测试功能
- **多种测试模式**: 快速测试、完整测试、多线程测试
- **智能分析**: 按分组统计成功率
- **详细报告**: 生成JSON格式的测试报告
- **交互式界面**: 支持命令行和交互式两种模式
- **指定频道测试**: 可测试特定频道

## 📁 项目结构

```
├── crawler.py              # 核心爬虫模块
├── m3u_generator.py        # M3U文件生成器
├── scheduler.py            # 定时任务调度器 (新增)
├── test_links.py           # 链接测试工具 (增强)
├── config.py               # 配置文件 (新增)
├── start_scheduler.bat     # Windows启动脚本 (新增)
├── cctv_m3u8_links.txt     # 央视频道本地数据
├── satellite_tv_links.txt  # 省级卫视本地数据
└── README.md               # 项目说明
```

## 🛠️ 安装依赖

```bash
pip install requests schedule
```

## 📖 使用方法

### 1. 定时任务调度器

#### Windows用户（推荐）
双击运行 `start_scheduler.bat`，选择运行模式：
- 选项1: 启动定时调度器（每3天自动更新）
- 选项2: 立即执行一次更新
- 选项3: 退出

#### 命令行方式
```bash
# 启动定时调度器
python scheduler.py

# 执行一次更新
python scheduler.py --once
```

### 2. 手动生成播放列表
```bash
python m3u_generator.py
```

### 3. 测试链接

#### 交互式模式
```bash
python test_links.py
```

#### 命令行模式
```bash
# 快速测试（随机5个频道）
python test_links.py --quick

# 测试所有频道
python test_links.py --all

# 多线程测试所有频道
python test_links.py --thread
```

## ⚙️ 配置说明

所有配置项都在 `config.py` 文件中：

### 定时任务配置
- `SCHEDULE_INTERVAL_DAYS`: 更新间隔天数（默认3天）
- `SCHEDULE_TIME`: 每日更新时间（默认09:00）
- `MAX_BACKUP_FILES`: 最大备份文件数（默认7个）

### 网络请求配置
- `REQUEST_TIMEOUT`: 请求超时时间（默认30秒）
- `MAX_RETRIES`: 最大重试次数（默认3次）
- `RETRY_DELAY`: 重试延迟（默认5秒）

### 测试配置
- `DEFAULT_TEST_COUNT`: 默认测试频道数（默认5个）
- `TEST_TIMEOUT`: 测试超时时间（默认10秒）
- `MAX_WORKERS`: 多线程最大工作线程数（默认5个）

### Git同步配置
- `GIT_REPO_URL`: Git仓库URL（需要修改为你的仓库地址）
- `GIT_USER_NAME`: Git用户名
- `GIT_USER_EMAIL`: Git用户邮箱
- `GIT_AUTO_SYNC`: 是否在定时任务中自动同步（默认False）
- `GIT_SYNC_FILES`: 要同步的文件列表

## 📊 频道统计

当前支持的频道：
- **央视频道**: 19个（CCTV-1至CCTV-17、CCTV-4K、CCTV-8K）
- **省级卫视**: 20个（湖南卫视、浙江卫视、江苏卫视等）
- **总计**: 39个高质量频道

## 🔧 高级功能

### 1. 自动备份
- 每次更新都会生成带时间戳的备份文件
- 自动清理超过7个的旧备份文件
- 主文件始终为 `iptv.m3u`

### 2. 智能测试
- 支持按分组统计成功率
- 生成详细的JSON测试报告
- 多线程并发测试提高效率
- 支持指定频道名称测试

### 3. 容错机制
- 网络请求失败自动重试
- 单个源失败不影响其他源
- 本地数据作为备用方案

## 📝 日志记录

所有操作都会记录到 `crawler.log` 文件中，包括：
- 爬取过程详情
- 错误和警告信息
- 定时任务执行记录
- 测试结果统计

## 🎯 使用建议

1. **首次使用**: 建议先运行 `python test_links.py --quick` 测试网络连接
2. **定时更新**: 使用 `start_scheduler.bat` 启动定时任务，保持播放列表最新
3. **网络问题**: 如果在线源访问困难，系统会自动使用本地数据
4. **自定义配置**: 根据需要修改 `config.py` 中的配置项

## 🔗 Git远程同步

### 配置Git同步

1. **修改配置文件** (`config.py`)：
```python
# Git同步配置
GIT_REPO_URL = "https://gitee.com/your-username/iptv-playlist.git"  # 你的仓库地址
GIT_USER_NAME = "your-username"                                     # 你的用户名
GIT_USER_EMAIL = "<EMAIL>"                          # 你的邮箱
GIT_AUTO_SYNC = True                                                # 启用自动同步
```

2. **配置Git凭证**：
```bash
git config --global user.name "your-username"
git config --global user.email "<EMAIL>"
git config --global credential.helper store
```

### 使用Git同步

#### 手动同步
```bash
# 基本同步
python git_sync.py

# 自定义配置同步
python git_sync.py --repo https://gitee.com/user/repo.git --user username --email <EMAIL>

# 仅配置Git用户信息
python git_sync.py --config
```

#### Windows用户
```bash
# 双击运行批处理文件
git_sync.bat
```

#### 自动同步
- 在 `config.py` 中设置 `GIT_AUTO_SYNC = True`
- 运行定时任务时会自动同步到Git仓库

#### 功能演示
```bash
# 查看配置指南
python git_sync_example.py --guide

# 交互式演示
python git_sync_example.py
```

### 同步的文件
- `iptv.m3u` - 主播放列表
- `README.md` - 项目说明
- `项目总结报告.md` - 项目报告
- `cctv_m3u8_links.txt` - 央视频道数据
- `satellite_tv_links.txt` - 省级卫视数据
- 最新的备份文件 (`iptv_*.m3u`)

## 🔄 更新日志

### v2.1 (2025-07-02)
- ✅ 新增Git远程同步功能
- ✅ 支持自动同步到GitHub/Gitee等Git仓库
- ✅ 集成到定时任务中，支持自动同步
- ✅ 提供Windows批处理脚本和使用示例
- ✅ 完善的配置管理和错误处理

### v2.0 (2025-07-02)
- ✅ 新增定时任务调度器
- ✅ 增加8个新的在线源
- ✅ 完全重写测试功能，支持多线程
- ✅ 新增配置文件统一管理
- ✅ 新增Windows批处理启动脚本
- ✅ 优化错误处理和日志记录

### v1.0
- ✅ 基础爬虫功能
- ✅ M3U文件生成
- ✅ 省级卫视频道支持
- ✅ 简单链接测试

## 📞 技术支持

如有问题或建议，请查看日志文件 `crawler.log` 获取详细信息。
