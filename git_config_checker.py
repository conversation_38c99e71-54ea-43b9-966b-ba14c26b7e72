#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git配置检查工具
用于验证和修复Git同步配置问题
"""

import os
import sys
import re
from pathlib import Path

def check_git_installation():
    """检查Git是否已安装"""
    print("🔍 检查Git安装状态...")
    try:
        import subprocess
        result = subprocess.run(["git", "--version"], capture_output=True, text=True, check=False)
        if result.returncode == 0:
            print(f"✅ Git已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Git未安装或无法访问")
            print("📋 请安装Git: https://git-scm.com/downloads")
            return False
    except Exception as e:
        print(f"❌ Git检查失败: {e}")
        return False

def check_config_file():
    """检查config.py配置文件"""
    print("\n🔍 检查config.py配置...")
    
    config_path = Path("config.py")
    if not config_path.exists():
        print("❌ config.py文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取Git配置
        git_configs = {}
        patterns = {
            'GIT_REPO_URL': r'GIT_REPO_URL\s*=\s*["\']([^"\']+)["\']',
            'GIT_USER_NAME': r'GIT_USER_NAME\s*=\s*["\']([^"\']+)["\']',
            'GIT_USER_EMAIL': r'GIT_USER_EMAIL\s*=\s*["\']([^"\']+)["\']',
            'GIT_AUTO_SYNC': r'GIT_AUTO_SYNC\s*=\s*(True|False)'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                git_configs[key] = match.group(1)
            else:
                git_configs[key] = None
        
        print("📋 当前Git配置:")
        for key, value in git_configs.items():
            if value:
                print(f"   {key}: {value}")
            else:
                print(f"   {key}: ❌ 未配置")
        
        return git_configs
        
    except Exception as e:
        print(f"❌ 读取config.py失败: {e}")
        return False

def validate_git_config(git_configs):
    """验证Git配置"""
    print("\n🔍 验证Git配置...")
    
    errors = []
    warnings = []
    
    # 验证仓库URL
    repo_url = git_configs.get('GIT_REPO_URL')
    if not repo_url:
        errors.append("GIT_REPO_URL 未配置")
    else:
        # 检查占位符
        placeholder_patterns = ['your-username', 'your-repo', 'example.com', 'test.git']
        for pattern in placeholder_patterns:
            if pattern in repo_url.lower():
                errors.append(f"GIT_REPO_URL 包含占位符 '{pattern}'")
                break
        
        # 检查URL格式
        valid_patterns = [
            r'https://github\.com/[\w\-\.]+/[\w\-\.]+\.git',
            r'https://gitee\.com/[\w\-\.]+/[\w\-\.]+\.git',
            r'git@github\.com:[\w\-\.]+/[\w\-\.]+\.git',
            r'git@gitee\.com:[\w\-\.]+/[\w\-\.]+\.git'
        ]
        
        if not any(re.match(pattern, repo_url) for pattern in valid_patterns):
            warnings.append("GIT_REPO_URL 格式可能不正确")
    
    # 验证用户名
    user_name = git_configs.get('GIT_USER_NAME')
    if not user_name:
        errors.append("GIT_USER_NAME 未配置")
    elif 'your-username' in user_name.lower():
        errors.append("GIT_USER_NAME 包含占位符")
    
    # 验证邮箱
    user_email = git_configs.get('GIT_USER_EMAIL')
    if not user_email:
        errors.append("GIT_USER_EMAIL 未配置")
    elif 'your-email' in user_email.lower() or '@example.com' in user_email.lower():
        errors.append("GIT_USER_EMAIL 包含占位符")
    elif not re.match(r'^[^@]+@[^@]+\.[^@]+$', user_email):
        warnings.append("GIT_USER_EMAIL 格式可能不正确")
    
    # 显示结果
    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"   - {error}")
    
    if warnings:
        print("⚠️  配置警告:")
        for warning in warnings:
            print(f"   - {warning}")
    
    if not errors and not warnings:
        print("✅ Git配置验证通过")
        return True
    
    return len(errors) == 0

def test_repository_access(repo_url):
    """测试仓库访问"""
    if not repo_url or 'your-username' in repo_url.lower():
        print("⏭️  跳过仓库访问测试（URL未正确配置）")
        return False
    
    print(f"\n🔍 测试仓库访问: {repo_url}")
    
    try:
        import subprocess
        
        # 测试仓库是否可访问
        result = subprocess.run(
            ["git", "ls-remote", "--heads", repo_url],
            capture_output=True,
            text=True,
            timeout=10,
            check=False
        )
        
        if result.returncode == 0:
            print("✅ 仓库访问正常")
            return True
        else:
            print("❌ 仓库访问失败:")
            print(f"   错误信息: {result.stderr.strip()}")
            
            # 分析错误类型
            stderr = result.stderr.lower()
            if "does not appear to be a git repository" in stderr:
                print("💡 可能的原因: 仓库不存在或URL错误")
            elif "permission denied" in stderr or "authentication" in stderr:
                print("💡 可能的原因: 认证失败或无访问权限")
            elif "could not resolve host" in stderr:
                print("💡 可能的原因: 网络连接问题")
            
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 仓库访问超时")
        return False
    except Exception as e:
        print(f"❌ 仓库访问测试失败: {e}")
        return False

def provide_configuration_guide():
    """提供配置指南"""
    print("\n📋 Git配置指南:")
    print("=" * 50)
    
    print("\n1️⃣ 创建Git仓库:")
    print("   GitHub: https://github.com/new")
    print("   Gitee:  https://gitee.com/projects/new")
    
    print("\n2️⃣ 修改config.py配置:")
    print("   # Git同步配置")
    print("   GIT_REPO_URL = \"https://gitee.com/your-username/iptv-playlist.git\"")
    print("   GIT_USER_NAME = \"your-username\"")
    print("   GIT_USER_EMAIL = \"<EMAIL>\"")
    print("   GIT_AUTO_SYNC = True")
    
    print("\n3️⃣ 配置Git凭证:")
    print("   git config --global user.name \"your-username\"")
    print("   git config --global user.email \"<EMAIL>\"")
    print("   git config --global credential.helper store")
    
    print("\n4️⃣ 测试同步:")
    print("   python git_sync.py --config")
    print("   python git_sync.py")

def main():
    """主函数"""
    print("🔧 Git配置检查工具")
    print("=" * 50)
    
    # 检查Git安装
    if not check_git_installation():
        return 1
    
    # 检查配置文件
    git_configs = check_config_file()
    if not git_configs:
        return 1
    
    # 验证配置
    config_valid = validate_git_config(git_configs)
    
    # 测试仓库访问
    repo_access = test_repository_access(git_configs.get('GIT_REPO_URL'))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果:")
    print(f"   Git安装: ✅")
    print(f"   配置文件: ✅")
    print(f"   配置验证: {'✅' if config_valid else '❌'}")
    print(f"   仓库访问: {'✅' if repo_access else '❌'}")
    
    if config_valid and repo_access:
        print("\n🎉 Git配置完全正常，可以使用同步功能！")
        print("🚀 运行命令: python git_sync.py")
        return 0
    else:
        print("\n⚠️  发现配置问题，请参考以下指南:")
        provide_configuration_guide()
        return 1

if __name__ == "__main__":
    sys.exit(main())
