#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git同步功能测试脚本
用于验证修复后的Git同步功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

def test_git_sync_import():
    """测试Git同步模块导入"""
    print("=== 测试模块导入 ===")
    try:
        from git_sync import GitSyncManager
        print("✅ Git同步模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ Git同步模块导入失败: {e}")
        return False

def test_git_sync_initialization():
    """测试Git同步管理器初始化"""
    print("\n=== 测试初始化 ===")
    try:
        from git_sync import GitSyncManager
        
        # 测试默认初始化
        sync_manager = GitSyncManager()
        print("✅ 默认初始化成功")
        
        # 测试自定义初始化
        sync_manager = GitSyncManager(
            repo_url="https://gitee.com/test/test.git",
            user_name="test_user",
            user_email="<EMAIL>"
        )
        print("✅ 自定义初始化成功")
        
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def test_file_existence_check():
    """测试文件存在性检查"""
    print("\n=== 测试文件存在性检查 ===")
    try:
        from git_sync import GitSyncManager
        sync_manager = GitSyncManager()
        
        # 测试存在的文件
        if sync_manager._file_exists("git_sync.py"):
            print("✅ 存在文件检查正确")
        else:
            print("❌ 存在文件检查错误")
            return False
        
        # 测试不存在的文件
        if not sync_manager._file_exists("non_existent_file.txt"):
            print("✅ 不存在文件检查正确")
        else:
            print("❌ 不存在文件检查错误")
            return False
        
        # 测试中文文件名
        if os.path.exists("项目总结报告.md"):
            if sync_manager._file_exists("项目总结报告.md"):
                print("✅ 中文文件名检查正确")
            else:
                print("❌ 中文文件名检查错误")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 文件存在性检查失败: {e}")
        return False

def test_command_execution():
    """测试命令执行"""
    print("\n=== 测试命令执行 ===")
    try:
        from git_sync import GitSyncManager
        sync_manager = GitSyncManager()
        
        # 测试简单命令 - 在Windows上使用cmd
        if os.name == 'nt':  # Windows
            returncode, stdout, stderr = sync_manager._run_command(["cmd", "/c", "echo", "test"], check=False)
        else:  # Unix/Linux
            returncode, stdout, stderr = sync_manager._run_command(["echo", "test"], check=False)

        if returncode == 0:
            print("✅ 简单命令执行成功")
        else:
            print(f"❌ 简单命令执行失败: {stderr}")
            return False
        
        # 测试Git版本命令
        returncode, stdout, stderr = sync_manager._run_command(["git", "--version"], check=False)
        if returncode == 0:
            print(f"✅ Git版本检查成功: {stdout.strip()}")
        else:
            print(f"❌ Git版本检查失败: {stderr}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 命令执行测试失败: {e}")
        return False

def test_prepare_files():
    """测试文件准备"""
    print("\n=== 测试文件准备 ===")
    try:
        from git_sync import GitSyncManager
        sync_manager = GitSyncManager()
        
        # 测试文件准备
        files = sync_manager.prepare_files()
        print(f"✅ 文件准备成功，找到 {len(files)} 个文件:")
        for file in files:
            print(f"   - {file}")
        
        return True
    except Exception as e:
        print(f"❌ 文件准备失败: {e}")
        return False

def test_git_user_config():
    """测试Git用户配置"""
    print("\n=== 测试Git用户配置 ===")
    original_dir = os.getcwd()
    temp_dir = None

    try:
        from git_sync import GitSyncManager

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        os.chdir(temp_dir)

        sync_manager = GitSyncManager(
            user_name="test_user",
            user_email="<EMAIL>"
        )

        # 初始化Git仓库
        returncode, _, _ = sync_manager._run_command(["git", "init"], check=False)
        if returncode == 0:
            print("✅ Git仓库初始化成功")

            # 配置用户信息
            sync_manager.configure_git_user()
            print("✅ Git用户配置成功")

            # 验证配置
            returncode, stdout, _ = sync_manager._run_command(["git", "config", "user.name"], check=False)
            if returncode == 0 and "test_user" in stdout:
                print("✅ 用户名配置验证成功")
                return True
            else:
                print("❌ 用户名配置验证失败")
                return False
        else:
            print("❌ Git仓库初始化失败")
            return False

    except Exception as e:
        print(f"❌ Git用户配置测试失败: {e}")
        return False
    finally:
        # 恢复原始目录
        os.chdir(original_dir)
        # 清理临时目录
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                print(f"⚠️  清理临时目录失败: {e}")

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始Git同步功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_git_sync_import),
        ("初始化", test_git_sync_initialization),
        ("文件存在性检查", test_file_existence_check),
        ("命令执行", test_command_execution),
        ("文件准备", test_prepare_files),
        ("Git用户配置", test_git_user_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Git同步功能修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

def main():
    """主函数"""
    # 保存当前目录
    original_dir = os.getcwd()
    
    try:
        # 切换到项目目录
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        # 运行测试
        success = run_all_tests()
        
        if success:
            print("\n🚀 可以尝试运行以下命令测试Git同步:")
            print("   python git_sync.py --config  # 配置Git用户信息")
            print("   python git_sync_example.py --guide  # 查看配置指南")
            
        return 0 if success else 1
        
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return 1
    finally:
        # 恢复原始目录
        os.chdir(original_dir)

if __name__ == "__main__":
    sys.exit(main())
