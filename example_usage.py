#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IPTV项目使用示例
演示各种功能的使用方法
"""

import os
import sys
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "="*50)
    print(f"  {title}")
    print("="*50)

def example_generate_playlist():
    """示例：生成播放列表"""
    print_header("生成IPTV播放列表")
    
    from m3u_generator import generate_m3u_playlist
    from crawler import fetch_cctv_m3u8_links, fetch_satellite_tv_links
    
    print("正在获取频道数据...")
    
    # 获取央视频道（优先使用在线源）
    cctv_links = fetch_cctv_m3u8_links(use_online=True)
    print(f"获取到 {len(cctv_links)} 个央视频道")
    
    # 获取省级卫视频道
    satellite_links = fetch_satellite_tv_links(use_online=True)
    print(f"获取到 {len(satellite_links)} 个省级卫视频道")
    
    # 生成M3U文件
    output_file = f"example_{datetime.now().strftime('%Y%m%d_%H%M%S')}.m3u"
    generate_m3u_playlist(cctv_links, satellite_links, output_file)
    
    print(f"✅ 播放列表已生成: {output_file}")
    print(f"   总频道数: {len(cctv_links) + len(satellite_links)}")

def example_test_links():
    """示例：测试链接"""
    print_header("测试IPTV链接")
    
    from test_links import test_m3u_file
    
    # 检查是否存在M3U文件
    if not os.path.exists("iptv.m3u"):
        print("❌ 未找到 iptv.m3u 文件，请先生成播放列表")
        return
    
    print("开始快速测试（随机5个频道）...")
    results = test_m3u_file(max_test=5)
    
    if results:
        valid_count = sum(1 for r in results if r["status"] == "valid")
        print(f"\n✅ 测试完成，有效频道: {valid_count}/{len(results)}")

def example_scheduler():
    """示例：定时任务"""
    print_header("定时任务调度器")
    
    from scheduler import IPTVScheduler
    
    print("创建调度器实例...")
    scheduler = IPTVScheduler()
    
    print("执行一次更新任务...")
    scheduler.run_once()
    
    print("✅ 定时任务示例完成")

def example_config():
    """示例：配置管理"""
    print_header("配置管理")
    
    from config import config
    
    print("当前配置信息:")
    print(f"  更新间隔: {config.SCHEDULE_INTERVAL_DAYS} 天")
    print(f"  更新时间: {config.SCHEDULE_TIME}")
    print(f"  请求超时: {config.REQUEST_TIMEOUT} 秒")
    print(f"  在线源数量: {len(config.ONLINE_SOURCES)} 个")
    print(f"  省级卫视数量: {len(config.SATELLITE_TV_SOURCES)} 个")
    
    print("\n在线源列表:")
    for i, source in enumerate(config.ONLINE_SOURCES[:3], 1):
        print(f"  {i}. {source}")
    print(f"  ... 还有 {len(config.ONLINE_SOURCES)-3} 个源")

def example_specific_channel_test():
    """示例：测试指定频道"""
    print_header("测试指定频道")
    
    from test_links import test_specific_channels
    
    # 检查是否存在M3U文件
    if not os.path.exists("iptv.m3u"):
        print("❌ 未找到 iptv.m3u 文件，请先生成播放列表")
        return
    
    # 测试几个常见频道
    test_channels = ["CCTV-1", "湖南卫视", "浙江卫视"]
    print(f"测试指定频道: {', '.join(test_channels)}")
    
    test_specific_channels(test_channels)

def main():
    """主函数"""
    print("🎬 IPTV项目功能演示")
    print("本脚本将演示项目的各种功能")
    
    examples = [
        ("1", "生成播放列表", example_generate_playlist),
        ("2", "测试链接", example_test_links),
        ("3", "定时任务", example_scheduler),
        ("4", "配置管理", example_config),
        ("5", "测试指定频道", example_specific_channel_test),
        ("0", "运行所有示例", None),
    ]
    
    print("\n可用的示例:")
    for code, desc, _ in examples:
        print(f"  {code}. {desc}")
    
    choice = input("\n请选择要运行的示例 (0-5): ").strip()
    
    if choice == "0":
        # 运行所有示例
        for code, desc, func in examples:
            if func:
                try:
                    func()
                except Exception as e:
                    print(f"❌ 示例 '{desc}' 运行失败: {e}")
                    
                input("\n按回车键继续下一个示例...")
    else:
        # 运行指定示例
        for code, desc, func in examples:
            if code == choice and func:
                try:
                    func()
                except Exception as e:
                    print(f"❌ 示例运行失败: {e}")
                break
        else:
            print("❌ 无效选择")
    
    print("\n🎉 演示完成！")

if __name__ == "__main__":
    main()
