@echo off
chcp 65001 >nul
title IPTV定时任务调度器

echo ========================================
echo           IPTV定时任务调度器
echo ========================================
echo.
echo 选择运行模式:
echo 1. 启动定时调度器 (每3天自动更新)
echo 2. 立即执行一次更新
echo 3. 退出
echo.

set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 正在启动定时调度器...
    python scheduler.py
) else if "%choice%"=="2" (
    echo.
    echo 正在执行一次性更新...
    python scheduler.py --once
) else if "%choice%"=="3" (
    echo.
    echo 退出程序
    exit /b 0
) else (
    echo.
    echo 无效选择，请重新运行
    pause
    exit /b 1
)

echo.
pause
